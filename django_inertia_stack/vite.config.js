import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    plugins: [
        vue(),
        laravel({
            input: ['resources/js/app.js'],
            refresh: true,
        }),
    ],
    server: {
        host: '127.0.0.1',
        port: 3000,
        hmr: {
            host: '127.0.0.1',
        },
    },
    build: {
        outDir: 'static/build',
        manifest: true,
        rollupOptions: {
            input: 'resources/js/app.js',
        },
    },
});
