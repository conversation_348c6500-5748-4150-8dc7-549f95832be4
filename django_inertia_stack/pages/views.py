from inertia import render


def home(request):
    """Home page view"""
    return render(request, 'Home', {
        'message': 'hello home',
        'page_name': 'home'
    })


def about(request):
    """About page view"""
    return render(request, 'About', {
        'message': 'hello about',
        'page_name': 'about'
    })


def contact(request):
    """Contact page view"""
    return render(request, 'Contact', {
        'message': 'hello contact',
        'page_name': 'contact'
    })


def services(request):
    """Services page view"""
    return render(request, 'Services', {
        'message': 'hello services',
        'page_name': 'services'
    })


def blog(request):
    """Blog page view"""
    return render(request, 'Blog', {
        'message': 'hello blog',
        'page_name': 'blog'
    })


def portfolio(request):
    """Portfolio page view"""
    return render(request, 'Portfolio', {
        'message': 'hello portfolio',
        'page_name': 'portfolio'
    })


def team(request):
    """Team page view"""
    return render(request, 'Team', {
        'message': 'hello team',
        'page_name': 'team'
    })


def pricing(request):
    """Pricing page view"""
    return render(request, 'Pricing', {
        'message': 'hello pricing',
        'page_name': 'pricing'
    })
