<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600">
    <nav class="bg-white shadow-lg">
      <div class="max-w-7xl mx-auto px-4">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <h1 class="text-xl font-bold text-gray-800">Django + Inertia Stack</h1>
          </div>
          <div class="flex items-center space-x-4">
            <Link href="/" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md">Home</Link>
            <Link href="/about/" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md">About</Link>
            <Link href="/services/" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md">Services</Link>
            <Link href="/contact/" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md">Contact</Link>
            <Link href="/blog/" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md">Blog</Link>
            <Link href="/portfolio/" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md">Portfolio</Link>
            <Link href="/team/" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md">Team</Link>
            <Link href="/pricing/" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md">Pricing</Link>
          </div>
        </div>
      </div>
    </nav>

    <div class="flex items-center justify-center min-h-screen -mt-16">
      <div class="text-center">
        <div class="bg-white rounded-lg shadow-2xl p-12 max-w-md mx-auto">
          <div class="mb-8">
            <div class="w-20 h-20 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
              </svg>
            </div>
            <h1 class="text-4xl font-bold text-gray-800 mb-2">{{ message }}</h1>
            <p class="text-gray-600">Welcome to the {{ page_name }} page</p>
          </div>
          
          <div class="space-y-4">
            <p class="text-gray-700">
              This is a Django + Inertia.js + Vue.js stack demo. 
              Navigate through different pages to see the "hello {page}" messages.
            </p>
            
            <div class="grid grid-cols-2 gap-2 mt-6">
              <Link href="/about/" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm transition-colors">
                About
              </Link>
              <Link href="/services/" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md text-sm transition-colors">
                Services
              </Link>
              <Link href="/contact/" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-md text-sm transition-colors">
                Contact
              </Link>
              <Link href="/blog/" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md text-sm transition-colors">
                Blog
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Link } from '@inertiajs/vue3'

export default {
  components: {
    Link,
  },
  props: {
    message: String,
    page_name: String,
  },
}
</script>
