<template>
  <div class="min-h-screen bg-gradient-to-br from-green-500 to-blue-600">
    <nav class="bg-white shadow-lg">
      <div class="max-w-7xl mx-auto px-4">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <h1 class="text-xl font-bold text-gray-800">Django + Inertia Stack</h1>
          </div>
          <div class="flex items-center space-x-4">
            <Link href="/" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md">Home</Link>
            <Link href="/about/" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md font-semibold">About</Link>
            <Link href="/services/" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md">Services</Link>
            <Link href="/contact/" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md">Contact</Link>
            <Link href="/blog/" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md">Blog</Link>
            <Link href="/portfolio/" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md">Portfolio</Link>
            <Link href="/team/" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md">Team</Link>
            <Link href="/pricing/" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md">Pricing</Link>
          </div>
        </div>
      </div>
    </nav>

    <div class="flex items-center justify-center min-h-screen -mt-16">
      <div class="text-center">
        <div class="bg-white rounded-lg shadow-2xl p-12 max-w-md mx-auto">
          <div class="mb-8">
            <div class="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <h1 class="text-4xl font-bold text-gray-800 mb-2">{{ message }}</h1>
            <p class="text-gray-600">You're on the {{ page_name }} page</p>
          </div>
          
          <div class="space-y-4">
            <p class="text-gray-700">
              This page demonstrates how Inertia.js seamlessly connects Django backend 
              with Vue.js frontend components.
            </p>
            
            <div class="bg-green-50 p-4 rounded-lg">
              <h3 class="font-semibold text-green-800 mb-2">About This Stack</h3>
              <ul class="text-sm text-green-700 space-y-1">
                <li>• Django backend with Inertia middleware</li>
                <li>• Vue.js 3 frontend components</li>
                <li>• Tailwind CSS for styling</li>
                <li>• Vite for fast development</li>
              </ul>
            </div>
            
            <Link href="/" class="inline-block bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-md transition-colors">
              Back to Home
            </Link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Link } from '@inertiajs/vue3'

export default {
  components: {
    Link,
  },
  props: {
    message: String,
    page_name: String,
  },
}
</script>
